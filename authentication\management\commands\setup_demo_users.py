from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from authentication.models import UserProfile

User = get_user_model()


class Command(BaseCommand):
    help = 'Create demo users for testing the role-based authentication system'

    def handle(self, *args, **options):
        # Update admin user role
        try:
            admin_user = User.objects.get(username='admin')
            admin_user.role = 'admin'
            admin_user.first_name = 'Admin'
            admin_user.last_name = 'User'
            admin_user.save()
            
            # Create profile if it doesn't exist
            profile, created = UserProfile.objects.get_or_create(
                user=admin_user,
                defaults={
                    'bio': 'System Administrator',
                    'company': 'Demo Company',
                    'department': 'IT'
                }
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated admin user role')
            )
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Admin user not found. Please create a superuser first.')
            )

        # Create manager user
        manager_user, created = User.objects.get_or_create(
            username='manager',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Manager',
                'last_name': 'User',
                'role': 'manager',
                'is_staff': False,
                'is_superuser': False
            }
        )
        
        if created:
            manager_user.set_password('manager123')
            manager_user.save()
            
            UserProfile.objects.create(
                user=manager_user,
                bio='Department Manager',
                company='Demo Company',
                department='Operations'
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created manager user: {manager_user.username}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Manager user already exists: {manager_user.username}')
            )

        # Create regular user
        regular_user, created = User.objects.get_or_create(
            username='user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Regular',
                'last_name': 'User',
                'role': 'user',
                'is_staff': False,
                'is_superuser': False
            }
        )
        
        if created:
            regular_user.set_password('user123')
            regular_user.save()
            
            UserProfile.objects.create(
                user=regular_user,
                bio='Regular system user',
                company='Demo Company',
                department='General'
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created regular user: {regular_user.username}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Regular user already exists: {regular_user.username}')
            )

        self.stdout.write(
            self.style.SUCCESS('\nDemo users setup complete!')
        )
        self.stdout.write('Login credentials:')
        self.stdout.write('Admin: admin / admin123')
        self.stdout.write('Manager: manager / manager123')
        self.stdout.write('User: user / user123')
