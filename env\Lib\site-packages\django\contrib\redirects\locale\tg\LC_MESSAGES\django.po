# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-15 00:33+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Самтдигаркунӣ(Redirects)"

msgid "site"
msgstr "сомона"

msgid "redirect from"
msgstr "Самтдигаркунӣ аз"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""

msgid "redirect to"
msgstr "Самтдигаркунӣ ба"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"“http://”."
msgstr ""

msgid "redirect"
msgstr "самтдигаркунӣ"

msgid "redirects"
msgstr "самтдигаркунӣ"
