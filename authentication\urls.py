from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'auth'

urlpatterns = [
    # API endpoints
    path('api/login/', views.LoginAPIView.as_view(), name='api_login'),
    path('api/logout/', views.LogoutAPIView.as_view(), name='api_logout'),
    path('api/token/', views.CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User management API (Admin only)
    path('api/users/', views.UserListCreateAPIView.as_view(), name='user_list_create'),
    path('api/users/<int:pk>/', views.UserDetailAPIView.as_view(), name='user_detail'),
    path('api/users/me/', views.CurrentUserAPIView.as_view(), name='current_user'),
    path('api/users/change-password/', views.ChangePasswordAPIView.as_view(), name='change_password'),
    
    # Template views
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
]
