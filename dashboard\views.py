from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator

from authentication.models import User
from authentication.permissions import IsAdminUser, IsManagerOrAdmin


class DashboardMixin(LoginRequiredMixin):
    """Base mixin for dashboard views"""
    login_url = '/auth/login/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context


class AdminDashboardView(DashboardMixin, TemplateView):
    """Admin dashboard view"""
    template_name = 'dashboard/admin_dashboard.html'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.role != 'admin':
            messages.error(request, 'Access denied. Admin privileges required.')
            return redirect('auth:login')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get user statistics
        total_users = User.objects.count()
        admin_users = User.objects.filter(role='admin').count()
        manager_users = User.objects.filter(role='manager').count()
        regular_users = User.objects.filter(role='user').count()
        active_users = User.objects.filter(is_active=True).count()

        # Get recent users
        recent_users = User.objects.order_by('-created_at')[:10]

        context.update({
            'total_users': total_users,
            'admin_users': admin_users,
            'manager_users': manager_users,
            'regular_users': regular_users,
            'active_users': active_users,
            'recent_users': recent_users,
        })

        return context


class ManagerDashboardView(DashboardMixin, TemplateView):
    """Manager dashboard view"""
    template_name = 'dashboard/manager_dashboard.html'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.role not in ['admin', 'manager']:
            messages.error(request, 'Access denied. Manager privileges required.')
            return redirect('auth:login')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Manager-specific data
        total_users = User.objects.filter(role='user').count()
        active_users = User.objects.filter(role='user', is_active=True).count()
        recent_users = User.objects.filter(role='user').order_by('-created_at')[:5]

        context.update({
            'total_users': total_users,
            'active_users': active_users,
            'recent_users': recent_users,
        })

        return context


class UserDashboardView(DashboardMixin, TemplateView):
    """Regular user dashboard view"""
    template_name = 'dashboard/user_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # User-specific data
        user = self.request.user
        context.update({
            'profile': getattr(user, 'profile', None),
        })

        return context


class UserManagementView(DashboardMixin, TemplateView):
    """User management view for admins"""
    template_name = 'dashboard/user_management.html'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.role != 'admin':
            messages.error(request, 'Access denied. Admin privileges required.')
            return redirect('auth:login')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all users with pagination
        users_list = User.objects.all().order_by('-created_at')
        paginator = Paginator(users_list, 10)  # Show 10 users per page

        page_number = self.request.GET.get('page')
        users = paginator.get_page(page_number)

        context.update({
            'users': users,
            'role_choices': User.ROLE_CHOICES,
        })

        return context


@login_required
def dashboard_redirect(request):
    """Redirect users to appropriate dashboard based on role"""
    return redirect(request.user.get_dashboard_url())
