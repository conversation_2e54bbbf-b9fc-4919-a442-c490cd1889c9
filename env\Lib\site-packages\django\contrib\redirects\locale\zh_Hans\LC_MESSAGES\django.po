# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON>白宦成 <xiq<PERSON><PERSON><PERSON>@gmail.com>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-07 04:22+0000\n"
"Last-Translator: Veoco <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Redirects"
msgstr "重定向"

msgid "site"
msgstr "站点"

msgid "redirect from"
msgstr "重定向自"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr "这应该是绝对路径，不包括域名。例如：“/events/search/”。"

msgid "redirect to"
msgstr "重定向到"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"这可以是绝对路径（如上所述），也可以是一个以 “https://” 等协议开头的完整 "
"URL。"

msgid "redirect"
msgstr "重定向"

msgid "redirects"
msgstr "重定向"
