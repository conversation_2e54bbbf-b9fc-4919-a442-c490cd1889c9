{% extends 'base.html' %}

{% block title %}Admin Dashboard - Role-Based Auth System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt text-danger"></i> Admin Dashboard
            <span class="badge bg-danger">Administrator</span>
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_users }}</h4>
                        <p class="mb-0">Total Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ active_users }}</h4>
                        <p class="mb-0">Active Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ manager_users }}</h4>
                        <p class="mb-0">Managers</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ admin_users }}</h4>
                        <p class="mb-0">Admins</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="{% url 'dashboard:user_management' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="showAddUserModal()">
                            <i class="fas fa-user-plus"></i> Add New User
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="/admin/" class="btn btn-outline-info w-100" target="_blank">
                            <i class="fas fa-cog"></i> Django Admin
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Users -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Recent Users</h5>
            </div>
            <div class="card-body">
                {% if recent_users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in recent_users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.first_name }} {{ user.last_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge bg-{{ user.role == 'admin' and 'danger' or user.role == 'manager' and 'warning' or 'info' }}">
                                        {{ user.get_role_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ user.is_active and 'success' or 'secondary' }}">
                                        {{ user.is_active and 'Active' or 'Inactive' }}
                                    </span>
                                </td>
                                <td>{{ user.created_at|date:"M d, Y" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No users found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function showAddUserModal() {
    // This would open a modal for adding users
    // For now, redirect to user management
    window.location.href = "{% url 'dashboard:user_management' %}";
}
</script>
{% endblock %}
