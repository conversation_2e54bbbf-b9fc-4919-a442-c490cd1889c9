#!/usr/bin/env python3
"""
Test script for the Django Role-Based Authentication API
"""

import requests
import json
import sys

BASE_URL = 'http://127.0.0.1:8000'

def test_login(username, password):
    """Test user login and return access token"""
    print(f"\n🔐 Testing login for user: {username}")
    
    response = requests.post(
        f'{BASE_URL}/auth/api/login/',
        json={'username': username, 'password': password}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Login successful!")
        print(f"   User ID: {data['user']['id']}")
        print(f"   Role: {data['user']['role']}")
        print(f"   Dashboard URL: {data['user']['dashboard_url']}")
        return data['access']
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return None

def test_user_list(token):
    """Test getting user list (admin only)"""
    print(f"\n👥 Testing user list endpoint")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{BASE_URL}/auth/api/users/', headers=headers)
    
    if response.status_code == 200:
        users = response.json()
        print(f"✅ User list retrieved successfully!")
        print(f"   Total users: {len(users['results']) if 'results' in users else len(users)}")
        
        # Display first few users
        user_list = users['results'] if 'results' in users else users
        for user in user_list[:3]:
            print(f"   - {user['username']} ({user['role']})")
    else:
        print(f"❌ Failed to get user list: {response.status_code}")
        print(f"   Error: {response.text}")

def test_current_user(token):
    """Test getting current user profile"""
    print(f"\n👤 Testing current user endpoint")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{BASE_URL}/auth/api/users/me/', headers=headers)
    
    if response.status_code == 200:
        user = response.json()
        print(f"✅ Current user profile retrieved!")
        print(f"   Username: {user['username']}")
        print(f"   Email: {user['email']}")
        print(f"   Role: {user['role']}")
        print(f"   Active: {user['is_active']}")
    else:
        print(f"❌ Failed to get current user: {response.status_code}")
        print(f"   Error: {response.text}")

def test_create_user(token):
    """Test creating a new user (admin only)"""
    print(f"\n➕ Testing user creation endpoint")
    
    headers = {'Authorization': f'Bearer {token}'}
    new_user_data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'role': 'user',
        'password': 'testpass123',
        'password_confirm': 'testpass123'
    }
    
    response = requests.post(
        f'{BASE_URL}/auth/api/users/',
        json=new_user_data,
        headers=headers
    )
    
    if response.status_code == 201:
        user = response.json()
        print(f"✅ User created successfully!")
        print(f"   Username: {user['username']}")
        print(f"   Email: {user['email']}")
        print(f"   Role: {user['role']}")
        return user['id']
    else:
        print(f"❌ Failed to create user: {response.status_code}")
        print(f"   Error: {response.text}")
        return None

def test_unauthorized_access():
    """Test accessing protected endpoint without token"""
    print(f"\n🚫 Testing unauthorized access")
    
    response = requests.get(f'{BASE_URL}/auth/api/users/')
    
    if response.status_code == 401:
        print(f"✅ Unauthorized access properly blocked!")
    else:
        print(f"❌ Unexpected response: {response.status_code}")

def main():
    """Run all API tests"""
    print("🚀 Starting Django Role-Based Authentication API Tests")
    print("=" * 60)
    
    # Test 1: Admin login
    admin_token = test_login('admin', 'admin123')
    if not admin_token:
        print("❌ Admin login failed, stopping tests")
        return
    
    # Test 2: Admin accessing user list
    test_user_list(admin_token)
    
    # Test 3: Admin getting current user profile
    test_current_user(admin_token)
    
    # Test 4: Admin creating new user
    new_user_id = test_create_user(admin_token)
    
    # Test 5: Manager login
    manager_token = test_login('manager', 'manager123')
    if manager_token:
        # Test 6: Manager trying to access user list (should fail)
        test_user_list(manager_token)
        
        # Test 7: Manager getting own profile
        test_current_user(manager_token)
    
    # Test 8: Regular user login
    user_token = test_login('user', 'user123')
    if user_token:
        # Test 9: User trying to access user list (should fail)
        test_user_list(user_token)
        
        # Test 10: User getting own profile
        test_current_user(user_token)
    
    # Test 11: Unauthorized access
    test_unauthorized_access()
    
    print("\n" + "=" * 60)
    print("🎉 API tests completed!")
    print("\n📝 Summary:")
    print("   - Admin users can manage all users")
    print("   - Manager users have limited access")
    print("   - Regular users can only access their own profile")
    print("   - Unauthorized access is properly blocked")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Error running tests: {e}")
        sys.exit(1)
