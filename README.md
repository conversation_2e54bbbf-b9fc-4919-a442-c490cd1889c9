# Django Role-Based Authentication System

A comprehensive Django REST Framework application with role-based authentication, JWT tokens, and responsive dashboards for different user roles.

## Features

### 🔐 Authentication & Authorization
- **JWT Token Authentication** using `djangorestframework-simplejwt`
- **Role-based Access Control** with three user roles:
  - **Admin**: Full system access, user management
  - **Manager**: Limited access, view user statistics
  - **User**: Basic access, personal dashboard
- **Custom User Model** with extended profile information
- **Secure Password Validation**

### 📊 Dashboards
- **Role-specific Dashboards** with different layouts and functionality
- **Admin Dashboard**: User statistics, user management, quick actions
- **Manager Dashboard**: User overview, reporting tools
- **User Dashboard**: Personal profile, account information

### 🎨 Frontend
- **Responsive Design** using Bootstrap 5
- **Modern UI** with Font Awesome icons
- **Role-based Navigation** showing appropriate menu items
- **Flash Messages** for user feedback

### 🔧 API Endpoints
- **RESTful API** for all user operations
- **JWT Authentication** for API access
- **Role-based Permissions** for API endpoints
- **User CRUD Operations** (Admin only)
- **Profile Management**

## Installation & Setup

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd role_based_auth
```

### 2. Create Virtual Environment
```bash
python -m venv env
# On Windows:
env\Scripts\activate
# On macOS/Linux:
source env/bin/activate
```

### 3. Install Dependencies
```bash
pip install django djangorestframework djangorestframework-simplejwt django-cors-headers Pillow
```

### 4. Run Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. Create Demo Users
```bash
python manage.py setup_demo_users
```

### 6. Start Development Server
```bash
python manage.py runserver
```

The application will be available at: `http://127.0.0.1:8000/`

## Demo Credentials

| Role | Username | Password | Access Level |
|------|----------|----------|--------------|
| Admin | `admin` | `admin123` | Full system access, user management |
| Manager | `manager` | `manager123` | User statistics, reporting |
| User | `user` | `user123` | Personal dashboard only |

## Project Structure

```
role_based_auth/
├── authentication/          # Authentication app
│   ├── models.py           # Custom User and UserProfile models
│   ├── serializers.py      # DRF serializers
│   ├── views.py            # API and template views
│   ├── permissions.py      # Custom permissions
│   ├── urls.py             # Authentication URLs
│   └── admin.py            # Admin configuration
├── dashboard/              # Dashboard app
│   ├── views.py            # Dashboard views
│   └── urls.py             # Dashboard URLs
├── templates/              # HTML templates
│   ├── base.html           # Base template
│   ├── authentication/     # Auth templates
│   └── dashboard/          # Dashboard templates
├── static/                 # Static files
│   └── css/                # Custom CSS
├── role_based_auth/        # Project settings
│   ├── settings.py         # Django settings
│   └── urls.py             # Main URL configuration
└── manage.py               # Django management script
```

## API Endpoints

### Authentication
- `POST /auth/api/login/` - User login (returns JWT tokens)
- `POST /auth/api/logout/` - User logout (blacklist token)
- `POST /auth/api/token/` - Get JWT token
- `POST /auth/api/token/refresh/` - Refresh JWT token

### User Management (Admin Only)
- `GET /auth/api/users/` - List all users
- `POST /auth/api/users/` - Create new user
- `GET /auth/api/users/{id}/` - Get user details
- `PUT /auth/api/users/{id}/` - Update user
- `DELETE /auth/api/users/{id}/` - Delete user

### Profile Management
- `GET /auth/api/users/me/` - Get current user profile
- `PUT /auth/api/users/me/` - Update current user profile
- `POST /auth/api/users/change-password/` - Change password

## Usage Examples

### Web Interface
1. Navigate to `http://127.0.0.1:8000/`
2. Login with demo credentials
3. Access role-specific dashboard
4. Admin users can manage other users

### API Usage
```bash
# Login and get JWT token
curl -X POST http://127.0.0.1:8000/auth/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Use token for authenticated requests
curl -X GET http://127.0.0.1:8000/auth/api/users/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Key Features Explained

### Role-Based Access Control
- **Admin**: Can create, read, update, delete all users
- **Manager**: Can view user statistics and generate reports
- **User**: Can only view and update their own profile

### JWT Authentication
- Access tokens expire in 60 minutes
- Refresh tokens expire in 1 day
- Automatic token rotation for security

### Custom User Model
- Extended Django's AbstractUser
- Added role field with choices
- Additional profile fields (phone, date of birth, etc.)

### Responsive Design
- Mobile-friendly interface
- Bootstrap 5 components
- Role-based UI elements

## Security Features

- **JWT Token Authentication**
- **Role-based Permissions**
- **CSRF Protection**
- **Password Validation**
- **Secure Headers**
- **CORS Configuration**

## Development

### Adding New Roles
1. Update `ROLE_CHOICES` in `authentication/models.py`
2. Create new permission classes in `authentication/permissions.py`
3. Add role-specific views and templates
4. Update navigation and dashboard logic

### Customizing Dashboards
- Edit templates in `templates/dashboard/`
- Modify view logic in `dashboard/views.py`
- Add new dashboard routes in `dashboard/urls.py`

## Production Deployment

1. Set `DEBUG = False` in settings
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure HTTPS
5. Set secure JWT signing key
6. Configure CORS for production domains

## License

This project is open source and available under the MIT License.
