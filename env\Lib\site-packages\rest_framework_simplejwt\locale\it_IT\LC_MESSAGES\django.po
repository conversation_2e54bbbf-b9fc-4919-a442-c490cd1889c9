# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"L'header di autorizzazione deve contenere due valori delimitati da uno spazio"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Il token dato non è valido per qualsiasi tipo di token"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Il token non conteneva nessuna informazione riconoscibile dell'utente"

#: authentication.py:132
msgid "User not found"
msgstr "Utente non trovato"

#: authentication.py:135
msgid "User is inactive"
msgstr "Utente non attivo"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Algoritmo di tipo '{}' non riconosciuto"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Devi avere installato cryptography per usare '{}'."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Il token non è valido o è scaduto"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "L'algoritmo specificato non è valido"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Il token non è valido o è scaduto"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Il token non è valido o è scaduto"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Nessun account attivo trovato con queste credenziali"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Nessun account attivo trovato con queste credenziali"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"L'impostazione '{}' è stata rimossa. Per favore utilizza '{}' per "
"visualizzare le impostazioni valide."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "utente"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "creato il"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "scade il"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Blacklist dei token"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Impossibile creare un token senza tipo o durata"

#: tokens.py:126
msgid "Token has no id"
msgstr "Il token non ha un id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Il token non ha un tipo"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Il token ha un tipo sbagliato"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Il token non contiene il parametro '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Il parametro '{}' del token è scaduto"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Il token è stato inserito nella blacklist"
