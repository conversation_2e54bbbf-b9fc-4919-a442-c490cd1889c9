{% extends 'base.html' %}

{% block title %}User Dashboard - Role-Based Auth System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt text-info"></i> User Dashboard
            <span class="badge bg-info">User</span>
        </h1>
    </div>
</div>

<!-- Welcome Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h3>Welcome back, {{ user.first_name|default:user.username }}!</h3>
                        <p class="mb-0">You're logged in as a regular user. Here's your personal dashboard.</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-user-circle fa-5x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> Profile Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>{{ user.username }}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>{{ user.email|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <td><strong>First Name:</strong></td>
                        <td>{{ user.first_name|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <td><strong>Last Name:</strong></td>
                        <td>{{ user.last_name|default:"Not provided" }}</td>
                    </tr>
                    <tr>
                        <td><strong>Role:</strong></td>
                        <td>
                            <span class="badge bg-info">{{ user.get_role_display }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Member Since:</strong></td>
                        <td>{{ user.created_at|date:"F d, Y" }}</td>
                    </tr>
                </table>
                
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit Profile
                    </button>
                    <button class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Account Activity</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ user.last_login|timesince|default:"Never" }}</h4>
                            <small class="text-muted">Last Login</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">Active</h4>
                        <small class="text-muted">Account Status</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <p class="text-muted mb-0">
                        <i class="fas fa-shield-alt text-success"></i>
                        Your account is secure and active
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-edit"></i> Update Profile
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-warning w-100">
                            <i class="fas fa-key"></i> Change Password
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-info w-100">
                            <i class="fas fa-download"></i> Download Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Information Notice -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> User Account</h5>
            <p class="mb-0">
                You have standard user privileges. If you need additional access or have questions, 
                please contact your system administrator.
            </p>
        </div>
    </div>
</div>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.opacity-50 {
    opacity: 0.5;
}
</style>
{% endblock %}
